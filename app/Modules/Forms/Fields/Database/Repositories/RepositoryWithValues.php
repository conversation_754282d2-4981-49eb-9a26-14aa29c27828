<?php

namespace AwardForce\Modules\Forms\Fields\Database\Repositories;

use AwardForce\Modules\Forms\Fields\Exceptions\CannotLoadFieldValue;
use Closure;
use Illuminate\Support\Collection;
use Platform\Search\HasValues;

interface RepositoryWithValues
{
    /**
     * @throws CannotLoadFieldValue
     */
    public function loadValuesForUpdate(HasValues $original);

    public function loadFieldValuesForObjects(array $objectIds, array $fieldSlugs, array $extraSelects = []): Collection;

    /**
     * Provide a method to exclude loading field values based on certain conditions.
     *
     * @return RepositoryWithValues
     */
    public function setFilter(Closure $filter);

    public function updateSingleField(HasValues $object, string $fieldSlug, $value, $hash, $protected);
}
